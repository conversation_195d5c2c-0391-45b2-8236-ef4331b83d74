# Model Master Troubleshooting Guide

## 🚨 **Quick Fix - Test First**

### **Step 1: Test JavaScript Directly**
1. **Open `Test-Direct.html`** in your browser
2. **Press F12** to open Developer Console
3. **Check for errors** in the Console tab
4. **You should see**:
   - Navigation bar with "Model Master"
   - Dashboard with statistics cards
   - No JavaScript errors

### **Step 2: If Test-Direct.html Works**
✅ **JavaScript is working** - Problem is with controller integration

### **Step 3: If Test-Direct.html Doesn't Work**
❌ **JavaScript has issues** - Check console errors

## 🔧 **Common Issues & Solutions**

### **Issue 1: "Cannot read properties of undefined"**
**Cause**: JavaScript file not loading properly
**Solution**:
```html
<!-- Make sure this line is in your HTML -->
<script src="Model.js"></script>
```

### **Issue 2: "Failed to load configuration"**
**Cause**: AJAX/Fetch requests failing
**Solution**:
- Use `Test-Direct.html` for testing
- For controller: Make sure your web server is running

### **Issue 3: "ModelMaster is not defined"**
**Cause**: JavaScript class not initializing
**Solution**:
```javascript
// Check if this line exists at the end of Model.js
document.addEventListener('DOMContentLoaded', () => {
    window.modelMaster = new ModelMaster();
});
```

### **Issue 4: CSS not loading**
**Cause**: CSS file path incorrect
**Solution**:
```html
<!-- Make sure this line is correct -->
<link href="Model.css" rel="stylesheet">
```

### **Issue 5: Controller not responding**
**Cause**: ASP.NET Core application not running
**Solution**:
```bash
# Start your application
dotnet run
# Or use Visual Studio F5
```

## 🎯 **Step-by-Step Debugging**

### **For Direct HTML Testing:**
1. **Open `Test-Direct.html`**
2. **Press F12** → Console tab
3. **Look for these messages**:
   ```
   ✅ "Model Master Test Page Loaded"
   ✅ "Model Master initialized successfully"
   ```
4. **If you see errors**, copy them and check solutions below

### **For Controller Testing:**
1. **Start your ASP.NET Core app**:
   ```bash
   dotnet run
   ```
2. **Open browser** to `https://localhost:5001/Home/Index`
3. **Press F12** → Console tab
4. **Check for AJAX errors**

## 🔍 **Console Error Solutions**

### **"Uncaught ReferenceError: ModelMaster is not defined"**
**Fix**: Add this to the end of Model.js:
```javascript
// Initialize Model Master when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modelMaster = new ModelMaster();
});
```

### **"Failed to fetch"**
**Fix**: Use embedded config or start web server:
```javascript
// Add this before loading Model.js
window.MODEL_CONFIG = { /* your config */ };
```

### **"Cannot read properties of null"**
**Fix**: Check HTML elements exist:
```html
<div id="navigation"></div>
<div id="content"></div>
```

## 🚀 **Quick Working Solution**

If nothing else works, use this minimal working version:

**Create: `Working-Test.html`**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Model Master Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="Model.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>Model Master Test</h1>
        <div id="navigation"></div>
        <div id="content"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Minimal test config
        window.MODEL_CONFIG = {
            appConfig: { title: "Test" },
            ui: { colors: { primary: "#007bff" } },
            navigation: { brand: "Test", menuItems: [] },
            dashboard: { stats: [] },
            products: { tableHeaders: [], sampleData: [] },
            forms: { productForm: { fields: [] } },
            messages: { success: {}, error: {}, confirm: {} }
        };
    </script>
    <script src="Model.js"></script>
</body>
</html>
```

## 📞 **Still Not Working?**

### **Check These Files Exist:**
- ✅ `Model.js` (JavaScript file)
- ✅ `Model.css` (CSS file)  
- ✅ `Model.json` (Configuration file)

### **Check Browser Console:**
1. **Press F12**
2. **Go to Console tab**
3. **Look for red error messages**
4. **Copy the exact error message**

### **Check Network Tab:**
1. **Press F12**
2. **Go to Network tab**
3. **Refresh page**
4. **Look for failed requests (red entries)**

### **Verify File Contents:**
- **Model.js** should start with: `class ModelMaster {`
- **Model.css** should start with: `:root {`
- **Model.json** should start with: `{`

## 🎯 **Expected Results**

When working correctly, you should see:
1. **Navigation bar** with "Model Master" brand
2. **Dashboard** with colored statistics cards
3. **Products page** with table when clicking "Products"
4. **No errors** in browser console

If you see these, your Model Master is working correctly! 🎉
