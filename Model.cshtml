@{
    ViewData["Title"] = "Model Master - Product Management System";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Adjust path as needed
}

@section Styles {
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS - Zero Hardcoded Values -->
    <link href="~/Model.css" rel="stylesheet">
}

<div class="model-master-container">
    <!-- Navigation Container - Dynamically Generated -->
    <div id="navigation"></div>

    <!-- Main Content Container - Dynamically Generated -->
    <div id="content"></div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="d-flex justify-content-center align-items-center position-fixed w-100 h-100" style="top: 0; left: 0; background: rgba(255,255,255,0.9); z-index: 9999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript - Zero Hardcoded Values -->
    <script src="~/Model.js"></script>

    <script>
        // Configure API endpoints for controller integration
        window.MODEL_API_CONFIG = {
            configUrl: '@Url.Action("GetConfig", "Home")',
            productsUrl: '@Url.Action("GetProducts", "Home")',
            addProductUrl: '@Url.Action("AddProduct", "Home")',
            updateProductUrl: '@Url.Action("UpdateProduct", "Home")',
            deleteProductUrl: '@Url.Action("DeleteProduct", "Home")'
        };

        // Set initial active view if specified
        @if (ViewBag.ActiveView != null)
        {
            <text>
            window.INITIAL_VIEW = '@ViewBag.ActiveView';
            </text>
        }

        // Hide loading spinner once everything is loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                const spinner = document.getElementById('loadingSpinner');
                if (spinner) {
                    spinner.style.display = 'none';
                }
            }, 500);
        });
    </script>
}
