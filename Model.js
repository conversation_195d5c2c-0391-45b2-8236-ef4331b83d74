/**
 * Model Master JavaScript - Zero Hardcoded Values
 * All configuration loaded from Model.json
 */

class ModelMaster {
    constructor() {
        this.config = null;
        this.currentView = 'dashboard';
        this.products = [];
        this.init();
    }

    async init() {
        try {
            await this.loadConfig();
            this.applyThemeFromConfig();
            this.renderNavigation();
            this.renderDashboard();
            this.bindEvents();
        } catch (error) {
            console.error('Failed to initialize Model Master:', error);
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('Model.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.config = await response.json();
            this.products = [...this.config.products.sampleData];
        } catch (error) {
            console.error('Failed to load configuration:', error);
            throw error;
        }
    }

    applyThemeFromConfig() {
        if (!this.config || !this.config.ui) return;

        const root = document.documentElement;
        const ui = this.config.ui;

        // Apply colors
        Object.entries(ui.colors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
        });

        // Apply spacing
        Object.entries(ui.spacing).forEach(([key, value]) => {
            root.style.setProperty(`--spacing-${key}`, value);
        });

        // Apply border radius
        Object.entries(ui.borderRadius).forEach(([key, value]) => {
            root.style.setProperty(`--border-radius-${key}`, value);
        });

        // Apply shadows
        Object.entries(ui.shadows).forEach(([key, value]) => {
            root.style.setProperty(`--shadow-${key}`, value);
        });

        // Apply fonts
        Object.entries(ui.fonts).forEach(([key, value]) => {
            root.style.setProperty(`--font-${key}`, value);
        });

        // Apply transitions
        Object.entries(ui.transitions).forEach(([key, value]) => {
            root.style.setProperty(`--transition-${key}`, value);
        });
    }

    renderNavigation() {
        const navContainer = document.getElementById('navigation');
        if (!navContainer || !this.config.navigation) return;

        const nav = this.config.navigation;
        let navHTML = `
            <nav class="navbar navbar-expand-lg model-navbar">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">${nav.brand}</a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
        `;

        nav.menuItems.forEach(item => {
            const activeClass = item.active ? 'active' : '';
            navHTML += `
                <li class="nav-item">
                    <a class="nav-link model-nav-item ${activeClass}" href="#" data-view="${item.id}">
                        <i class="${item.icon}"></i> ${item.label}
                    </a>
                </li>
            `;
        });

        navHTML += `
                        </ul>
                    </div>
                </div>
            </nav>
        `;

        navContainer.innerHTML = navHTML;
    }

    renderDashboard() {
        const contentContainer = document.getElementById('content');
        if (!contentContainer || !this.config.dashboard) return;

        const dashboard = this.config.dashboard;
        let dashboardHTML = `
            <div class="stats-container">
                <div class="row">
        `;

        dashboard.stats.forEach(stat => {
            dashboardHTML += `
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stat-card stat-card-${stat.color}">
                        <div class="card-body">
                            <div class="stat-card-icon">
                                <i class="${stat.icon}"></i>
                            </div>
                            <div class="stat-card-title">${stat.title}</div>
                            <div class="stat-card-value">${stat.value}</div>
                            <div class="stat-card-trend trend-${stat.trendDirection}">${stat.trend}</div>
                        </div>
                    </div>
                </div>
            `;
        });

        dashboardHTML += `
                </div>
            </div>
        `;

        contentContainer.innerHTML = dashboardHTML;
    }

    renderProducts() {
        const contentContainer = document.getElementById('content');
        if (!contentContainer || !this.config.products) return;

        const products = this.config.products;
        let productsHTML = `
            <div class="products-container">
                <div class="products-header">
                    <h2 class="products-title">${this.config.appConfig.title} - Products</h2>
                    <p class="products-subtitle">Manage your product inventory</p>
                    <button class="btn btn-primary-custom" onclick="modelMaster.showAddProductModal()">
                        <i class="fas fa-plus"></i> Add New Product
                    </button>
                </div>
                <div class="products-table-container">
                    <table class="table products-table">
                        <thead>
                            <tr>
        `;

        // Render table headers
        products.tableHeaders.forEach(header => {
            const sortIcon = header.sortable ? '<i class="fas fa-sort"></i>' : '';
            productsHTML += `<th style="width: ${header.width}">${header.label} ${sortIcon}</th>`;
        });

        productsHTML += `
                            </tr>
                        </thead>
                        <tbody>
        `;

        // Render table rows
        this.products.forEach(product => {
            const statusClass = `status-${product.status.replace('-', '-')}`;
            productsHTML += `
                <tr>
                    <td>${product.id}</td>
                    <td>${product.name}</td>
                    <td>${product.model}</td>
                    <td>${product.category}</td>
                    <td>${product.price}</td>
                    <td>${product.stock}</td>
                    <td><span class="status-badge ${statusClass}">${product.status.replace('-', ' ')}</span></td>
                    <td>
                        <button class="action-btn action-btn-edit" onclick="modelMaster.editProduct('${product.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn action-btn-delete" onclick="modelMaster.deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        productsHTML += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        contentContainer.innerHTML = productsHTML;
    }

    bindEvents() {
        // Navigation events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.model-nav-item') || e.target.closest('.model-nav-item')) {
                e.preventDefault();
                const navItem = e.target.matches('.model-nav-item') ? e.target : e.target.closest('.model-nav-item');
                const view = navItem.getAttribute('data-view');
                this.switchView(view);
            }
        });
    }

    switchView(view) {
        // Update navigation active state
        document.querySelectorAll('.model-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // Update current view
        this.currentView = view;

        // Render appropriate content
        switch (view) {
            case 'dashboard':
                this.renderDashboard();
                break;
            case 'products':
                this.renderProducts();
                break;
            case 'categories':
                this.renderCategories();
                break;
            case 'analytics':
                this.renderAnalytics();
                break;
            case 'settings':
                this.renderSettings();
                break;
            default:
                this.renderDashboard();
        }
    }

    renderCategories() {
        const contentContainer = document.getElementById('content');
        if (!contentContainer) return;

        contentContainer.innerHTML = `
            <div class="products-container">
                <div class="products-header">
                    <h2 class="products-title">Categories</h2>
                    <p class="products-subtitle">Manage product categories</p>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Categories management coming soon!
                </div>
            </div>
        `;
    }

    renderAnalytics() {
        const contentContainer = document.getElementById('content');
        if (!contentContainer) return;

        contentContainer.innerHTML = `
            <div class="products-container">
                <div class="products-header">
                    <h2 class="products-title">Analytics</h2>
                    <p class="products-subtitle">View detailed analytics and reports</p>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-chart-bar"></i> Analytics dashboard coming soon!
                </div>
            </div>
        `;
    }

    renderSettings() {
        const contentContainer = document.getElementById('content');
        if (!contentContainer) return;

        contentContainer.innerHTML = `
            <div class="products-container">
                <div class="products-header">
                    <h2 class="products-title">Settings</h2>
                    <p class="products-subtitle">Configure application settings</p>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-cog"></i> Settings panel coming soon!
                </div>
            </div>
        `;
    }

    showAddProductModal() {
        if (!this.config.forms || !this.config.forms.productForm) return;

        const form = this.config.forms.productForm;
        let modalHTML = `
            <div class="modal fade" id="productModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content modal-content-custom">
                        <div class="modal-header modal-header-custom">
                            <h5 class="modal-title">${form.title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body modal-body-custom">
                            <form id="productForm">
        `;

        form.fields.forEach(field => {
            modalHTML += `<div class="form-group">`;
            modalHTML += `<label class="form-label" for="${field.id}">${field.label}${field.required ? ' *' : ''}</label>`;

            if (field.type === 'select') {
                modalHTML += `<select class="form-control" id="${field.id}" ${field.required ? 'required' : ''}>`;
                modalHTML += `<option value="">Select ${field.label}</option>`;
                field.options.forEach(option => {
                    modalHTML += `<option value="${option.value}">${option.label}</option>`;
                });
                modalHTML += `</select>`;
            } else if (field.type === 'textarea') {
                modalHTML += `<textarea class="form-control" id="${field.id}" placeholder="${field.placeholder}" rows="${field.rows}" ${field.required ? 'required' : ''}></textarea>`;
            } else {
                modalHTML += `<input type="${field.type}" class="form-control" id="${field.id}" placeholder="${field.placeholder}" ${field.step ? `step="${field.step}"` : ''} ${field.required ? 'required' : ''}>`;
            }

            modalHTML += `</div>`;
        });

        modalHTML += `
                            </form>
                        </div>
                        <div class="modal-footer modal-footer-custom">
                            <button type="button" class="btn btn-secondary-custom" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary-custom" onclick="modelMaster.saveProduct()">Save Product</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('productModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('productModal'));
        modal.show();
    }

    saveProduct() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);

        // Basic validation
        const requiredFields = this.config.forms.productForm.fields.filter(field => field.required);
        let isValid = true;

        requiredFields.forEach(field => {
            const input = document.getElementById(field.id);
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        if (!isValid) {
            this.showMessage('error', this.config.messages.error.requiredField);
            return;
        }

        // Create new product
        const newProduct = {
            id: 'P' + String(this.products.length + 1).padStart(3, '0'),
            name: document.getElementById('productName').value,
            model: document.getElementById('modelNumber').value,
            category: document.getElementById('category').value,
            price: '$' + parseFloat(document.getElementById('price').value).toFixed(2),
            stock: parseInt(document.getElementById('stock').value),
            status: parseInt(document.getElementById('stock').value) > 0 ? 'active' : 'out-of-stock'
        };

        this.products.push(newProduct);
        this.renderProducts();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
        modal.hide();

        this.showMessage('success', this.config.messages.success.productAdded);
    }

    editProduct(productId) {
        // Implementation for editing products
        console.log('Edit product:', productId);
    }

    deleteProduct(productId) {
        if (confirm(this.config.messages.confirm.deleteProduct)) {
            this.products = this.products.filter(product => product.id !== productId);
            this.renderProducts();
            this.showMessage('success', this.config.messages.success.productDeleted);
        }
    }

    showMessage(type, message) {
        // Create and show toast message
        const toastHTML = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Initialize Model Master when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modelMaster = new ModelMaster();
});
