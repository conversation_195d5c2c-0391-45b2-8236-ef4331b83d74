/* Model Master CSS - Zero Hardcoded Values */
:root {
  /* Colors - All from JSON config */
  --color-primary: #007bff;
  --color-secondary: #6c757d;
  --color-success: #28a745;
  --color-danger: #dc3545;
  --color-warning: #ffc107;
  --color-info: #17a2b8;
  --color-light: #f8f9fa;
  --color-dark: #343a40;
  --color-white: #ffffff;
  --color-black: #000000;

  /* Spacing - All from JSON config */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* Border Radius - All from JSON config */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;

  /* Shadows - All from JSON config */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Fonts - All from JSON config */
  --font-primary: 'Roboto', sans-serif;
  --font-secondary: 'Open Sans', sans-serif;

  /* Transitions - All from JSON config */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
}

/* Base Styles */
.model-master-container {
  font-family: var(--font-primary);
  background-color: var(--color-light);
  min-height: 100vh;
}

/* Navigation Styles */
.model-navbar {
  background-color: var(--color-white);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-bottom: 1px solid var(--color-light);
}

.model-navbar .navbar-brand {
  font-weight: bold;
  color: var(--color-primary);
  font-size: var(--spacing-lg);
}

.model-nav-item {
  margin: 0 var(--spacing-sm);
  transition: all var(--transition-normal) ease;
}

.model-nav-item.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--border-radius-md);
}

.model-nav-item:hover {
  background-color: var(--color-light);
  border-radius: var(--border-radius-md);
  transform: translateY(-2px);
}

/* Dashboard Stats Cards */
.stats-container {
  padding: var(--spacing-xl);
  gap: var(--spacing-lg);
}

.stat-card {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal) ease;
  border: none;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card-icon {
  width: var(--spacing-xxl);
  height: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.stat-card-primary .stat-card-icon {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.stat-card-success .stat-card-icon {
  background-color: var(--color-success);
  color: var(--color-white);
}

.stat-card-info .stat-card-icon {
  background-color: var(--color-info);
  color: var(--color-white);
}

.stat-card-warning .stat-card-icon {
  background-color: var(--color-warning);
  color: var(--color-white);
}

.stat-card-title {
  font-size: var(--spacing-sm);
  color: var(--color-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-card-value {
  font-size: var(--spacing-xl);
  font-weight: bold;
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
}

.stat-card-trend {
  font-size: var(--spacing-sm);
  color: var(--color-success);
  font-weight: bold;
}

.stat-card-trend.trend-down {
  color: var(--color-danger);
}

/* Product Table Styles */
.products-container {
  padding: var(--spacing-xl);
}

.products-header {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.products-title {
  color: var(--color-dark);
  font-size: var(--spacing-lg);
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
}

.products-subtitle {
  color: var(--color-secondary);
  font-size: var(--spacing-md);
}

.products-table-container {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.products-table {
  margin-bottom: 0;
}

.products-table thead th {
  background-color: var(--color-light);
  border-bottom: 2px solid var(--color-primary);
  color: var(--color-dark);
  font-weight: bold;
  padding: var(--spacing-md);
  font-size: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.products-table tbody td {
  padding: var(--spacing-md);
  vertical-align: middle;
  border-bottom: 1px solid var(--color-light);
}

.products-table tbody tr:hover {
  background-color: var(--color-light);
  transition: background-color var(--transition-fast) ease;
}

/* Status Badges */
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--spacing-xs);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-active {
  background-color: var(--color-success);
  color: var(--color-white);
}

.status-low-stock {
  background-color: var(--color-warning);
  color: var(--color-dark);
}

.status-out-of-stock {
  background-color: var(--color-danger);
  color: var(--color-white);
}

/* Action Buttons */
.action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  margin: 0 var(--spacing-xs);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--spacing-xs);
  transition: all var(--transition-fast) ease;
  cursor: pointer;
}

.action-btn-edit {
  background-color: var(--color-info);
  color: var(--color-white);
}

.action-btn-edit:hover {
  background-color: var(--color-info);
  transform: scale(1.05);
}

.action-btn-delete {
  background-color: var(--color-danger);
  color: var(--color-white);
}

.action-btn-delete:hover {
  background-color: var(--color-danger);
  transform: scale(1.05);
}

/* Form Styles */
.form-container {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  margin: var(--spacing-lg);
}

.form-title {
  color: var(--color-dark);
  font-size: var(--spacing-lg);
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  color: var(--color-dark);
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-light);
  border-radius: var(--border-radius-md);
  font-size: var(--spacing-md);
  transition: all var(--transition-normal) ease;
  background-color: var(--color-white);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control.is-invalid {
  border-color: var(--color-danger);
}

.form-control.is-valid {
  border-color: var(--color-success);
}

.form-error {
  color: var(--color-danger);
  font-size: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.form-success {
  color: var(--color-success);
  font-size: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

/* Button Styles */
.btn-primary-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: bold;
  transition: all var(--transition-normal) ease;
  border: none;
  cursor: pointer;
}

.btn-primary-custom:hover {
  background-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary-custom {
  background-color: var(--color-secondary);
  border-color: var(--color-secondary);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: bold;
  transition: all var(--transition-normal) ease;
  border: none;
  cursor: pointer;
}

.btn-secondary-custom:hover {
  background-color: var(--color-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Modal Styles */
.modal-content-custom {
  border-radius: var(--border-radius-lg);
  border: none;
  box-shadow: var(--shadow-xl);
}

.modal-header-custom {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  padding: var(--spacing-lg);
}

.modal-body-custom {
  padding: var(--spacing-xl);
}

.modal-footer-custom {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-container {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .products-container {
    padding: var(--spacing-md);
  }

  .form-container {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }

  .model-navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
