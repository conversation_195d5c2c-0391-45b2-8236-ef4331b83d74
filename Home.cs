using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.IO;

namespace ModelMaster.Controllers
{
    public class HomeController : Controller
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public HomeController(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        // Main Model Master Dashboard
        public IActionResult Index()
        {
            return View("Model");
        }

        // Model Master Main Page
        public IActionResult Model()
        {
            return View();
        }

        // Dashboard View
        public IActionResult Dashboard()
        {
            return View("Model");
        }

        // Products Management
        public IActionResult Products()
        {
            ViewBag.ActiveView = "products";
            return View("Model");
        }

        // Categories Management
        public IActionResult Categories()
        {
            ViewBag.ActiveView = "categories";
            return View("Model");
        }

        // Analytics View
        public IActionResult Analytics()
        {
            ViewBag.ActiveView = "analytics";
            return View("Model");
        }

        // Settings View
        public IActionResult Settings()
        {
            ViewBag.ActiveView = "settings";
            return View("Model");
        }

        // API endpoint to get configuration
        [HttpGet]
        public IActionResult GetConfig()
        {
            try
            {
                var configPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Model.json");
                if (System.IO.File.Exists(configPath))
                {
                    var configContent = System.IO.File.ReadAllText(configPath);
                    return Content(configContent, "application/json");
                }
                else
                {
                    // Return embedded configuration if file doesn't exist
                    return Json(GetEmbeddedConfig());
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"Error loading configuration: {ex.Message}");
            }
        }

        // API endpoint to get products
        [HttpGet]
        public IActionResult GetProducts()
        {
            try
            {
                var config = GetConfigData();
                return Json(config.Products?.SampleData ?? new List<object>());
            }
            catch (Exception ex)
            {
                return BadRequest($"Error loading products: {ex.Message}");
            }
        }

        // API endpoint to add product
        [HttpPost]
        public IActionResult AddProduct([FromBody] ProductModel product)
        {
            try
            {
                // Here you would typically save to database
                // For now, we'll just return success
                return Json(new { success = true, message = "Product added successfully!" });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error adding product: {ex.Message}");
            }
        }

        // API endpoint to update product
        [HttpPut]
        public IActionResult UpdateProduct(string id, [FromBody] ProductModel product)
        {
            try
            {
                // Here you would typically update in database
                return Json(new { success = true, message = "Product updated successfully!" });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error updating product: {ex.Message}");
            }
        }

        // API endpoint to delete product
        [HttpDelete]
        public IActionResult DeleteProduct(string id)
        {
            try
            {
                // Here you would typically delete from database
                return Json(new { success = true, message = "Product deleted successfully!" });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error deleting product: {ex.Message}");
            }
        }

        // Helper method to get configuration data
        private dynamic GetConfigData()
        {
            var configPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Model.json");
            if (System.IO.File.Exists(configPath))
            {
                var configContent = System.IO.File.ReadAllText(configPath);
                return JsonSerializer.Deserialize<dynamic>(configContent);
            }
            return GetEmbeddedConfig();
        }

        // Embedded configuration as fallback
        private object GetEmbeddedConfig()
        {
            return new
            {
                AppConfig = new
                {
                    Title = "Model Master",
                    Subtitle = "Product Management System",
                    Version = "1.0.0",
                    Theme = "modern"
                },
                UI = new
                {
                    Colors = new
                    {
                        Primary = "#007bff",
                        Secondary = "#6c757d",
                        Success = "#28a745",
                        Danger = "#dc3545",
                        Warning = "#ffc107",
                        Info = "#17a2b8",
                        Light = "#f8f9fa",
                        Dark = "#343a40",
                        White = "#ffffff",
                        Black = "#000000"
                    },
                    Spacing = new
                    {
                        Xs = "0.25rem",
                        Sm = "0.5rem",
                        Md = "1rem",
                        Lg = "1.5rem",
                        Xl = "2rem",
                        Xxl = "3rem"
                    },
                    BorderRadius = new
                    {
                        Sm = "0.25rem",
                        Md = "0.375rem",
                        Lg = "0.5rem",
                        Xl = "0.75rem"
                    },
                    Shadows = new
                    {
                        Sm = "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
                        Md = "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                        Lg = "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                        Xl = "0 20px 25px -5px rgba(0, 0, 0, 0.1)"
                    },
                    Fonts = new
                    {
                        Primary = "'Roboto', sans-serif",
                        Secondary = "'Open Sans', sans-serif"
                    },
                    Transitions = new
                    {
                        Fast = "0.15s",
                        Normal = "0.3s",
                        Slow = "0.5s"
                    }
                }
            };
        }
    }

    // Product model for API operations
    public class ProductModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Model { get; set; }
        public string Category { get; set; }
        public string Price { get; set; }
        public int Stock { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
    }
}
