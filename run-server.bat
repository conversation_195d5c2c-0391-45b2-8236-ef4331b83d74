@echo off
echo Starting Model Master Local Server...
echo.
echo Choose an option:
echo 1. Python 3 (if installed)
echo 2. Node.js (if installed)
echo 3. PHP (if installed)
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Starting Python HTTP Server on port 8000...
    echo Open your browser and go to: http://localhost:8000/Model.cshtml
    python -m http.server 8000
) else if "%choice%"=="2" (
    echo Starting Node.js HTTP Server on port 8000...
    echo Open your browser and go to: http://localhost:8000/Model.cshtml
    npx http-server -p 8000
) else if "%choice%"=="3" (
    echo Starting PHP Built-in Server on port 8000...
    echo Open your browser and go to: http://localhost:8000/Model.cshtml
    php -S localhost:8000
) else (
    echo Invalid choice. Please run the script again.
    pause
)

pause
