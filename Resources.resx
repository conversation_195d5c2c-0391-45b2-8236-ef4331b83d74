<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Application Strings -->
  <data name="AppTitle" xml:space="preserve">
    <value>Model Master</value>
    <comment>Main application title</comment>
  </data>
  <data name="AppSubtitle" xml:space="preserve">
    <value>Product Management System</value>
    <comment>Application subtitle</comment>
  </data>

  <!-- Navigation Strings -->
  <data name="NavDashboard" xml:space="preserve">
    <value>Dashboard</value>
    <comment>Dashboard navigation item</comment>
  </data>
  <data name="NavProducts" xml:space="preserve">
    <value>Products</value>
    <comment>Products navigation item</comment>
  </data>
  <data name="NavCategories" xml:space="preserve">
    <value>Categories</value>
    <comment>Categories navigation item</comment>
  </data>
  <data name="NavAnalytics" xml:space="preserve">
    <value>Analytics</value>
    <comment>Analytics navigation item</comment>
  </data>
  <data name="NavSettings" xml:space="preserve">
    <value>Settings</value>
    <comment>Settings navigation item</comment>
  </data>

  <!-- Dashboard Strings -->
  <data name="StatsTotalProducts" xml:space="preserve">
    <value>Total Products</value>
    <comment>Total products statistic label</comment>
  </data>
  <data name="StatsActiveModels" xml:space="preserve">
    <value>Active Models</value>
    <comment>Active models statistic label</comment>
  </data>
  <data name="StatsCategories" xml:space="preserve">
    <value>Categories</value>
    <comment>Categories statistic label</comment>
  </data>
  <data name="StatsRevenue" xml:space="preserve">
    <value>Revenue</value>
    <comment>Revenue statistic label</comment>
  </data>

  <!-- Product Management Strings -->
  <data name="ProductsTitle" xml:space="preserve">
    <value>Products</value>
    <comment>Products page title</comment>
  </data>
  <data name="ProductsSubtitle" xml:space="preserve">
    <value>Manage your product inventory</value>
    <comment>Products page subtitle</comment>
  </data>
  <data name="AddNewProduct" xml:space="preserve">
    <value>Add New Product</value>
    <comment>Add new product button text</comment>
  </data>

  <!-- Form Strings -->
  <data name="ProductName" xml:space="preserve">
    <value>Product Name</value>
    <comment>Product name form label</comment>
  </data>
  <data name="ModelNumber" xml:space="preserve">
    <value>Model Number</value>
    <comment>Model number form label</comment>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
    <comment>Category form label</comment>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
    <comment>Price form label</comment>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
    <comment>Stock form label</comment>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
    <comment>Status form label</comment>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
    <comment>Actions column header</comment>
  </data>

  <!-- Message Strings -->
  <data name="ProductAddedSuccess" xml:space="preserve">
    <value>Product added successfully!</value>
    <comment>Success message for adding product</comment>
  </data>
  <data name="ProductUpdatedSuccess" xml:space="preserve">
    <value>Product updated successfully!</value>
    <comment>Success message for updating product</comment>
  </data>
  <data name="ProductDeletedSuccess" xml:space="preserve">
    <value>Product deleted successfully!</value>
    <comment>Success message for deleting product</comment>
  </data>
  <data name="RequiredFieldError" xml:space="preserve">
    <value>This field is required</value>
    <comment>Error message for required fields</comment>
  </data>
  <data name="DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this product?</value>
    <comment>Confirmation message for deleting product</comment>
  </data>

  <!-- Status Strings -->
  <data name="StatusActive" xml:space="preserve">
    <value>Active</value>
    <comment>Active status label</comment>
  </data>
  <data name="StatusLowStock" xml:space="preserve">
    <value>Low Stock</value>
    <comment>Low stock status label</comment>
  </data>
  <data name="StatusOutOfStock" xml:space="preserve">
    <value>Out of Stock</value>
    <comment>Out of stock status label</comment>
  </data>
</root>
