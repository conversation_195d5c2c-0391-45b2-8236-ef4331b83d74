<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Master - Product Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS - Zero Hardcoded Values -->
    <link href="Model.css" rel="stylesheet">
</head>
<body>
    <div class="model-master-container">
        <!-- Navigation Container - Dynamically Generated -->
        <div id="navigation"></div>
        
        <!-- Main Content Container - Dynamically Generated -->
        <div id="content"></div>
        
        <!-- Loading Spinner -->
        <div id="loadingSpinner" class="d-flex justify-content-center align-items-center position-fixed w-100 h-100" style="top: 0; left: 0; background: rgba(255,255,255,0.9); z-index: 9999;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Embedded Configuration - No External JSON File Needed -->
    <script>
        // Embedded configuration to avoid CORS issues
        const MODEL_CONFIG = {
            "appConfig": {
                "title": "Model Master",
                "subtitle": "Product Management System",
                "version": "1.0.0",
                "theme": "modern"
            },
            "ui": {
                "colors": {
                    "primary": "#007bff",
                    "secondary": "#6c757d",
                    "success": "#28a745",
                    "danger": "#dc3545",
                    "warning": "#ffc107",
                    "info": "#17a2b8",
                    "light": "#f8f9fa",
                    "dark": "#343a40",
                    "white": "#ffffff",
                    "black": "#000000"
                },
                "spacing": {
                    "xs": "0.25rem",
                    "sm": "0.5rem",
                    "md": "1rem",
                    "lg": "1.5rem",
                    "xl": "2rem",
                    "xxl": "3rem"
                },
                "borderRadius": {
                    "sm": "0.25rem",
                    "md": "0.375rem",
                    "lg": "0.5rem",
                    "xl": "0.75rem"
                },
                "shadows": {
                    "sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
                    "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                    "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"
                },
                "fonts": {
                    "primary": "'Roboto', sans-serif",
                    "secondary": "'Open Sans', sans-serif"
                },
                "transitions": {
                    "fast": "0.15s",
                    "normal": "0.3s",
                    "slow": "0.5s"
                }
            },
            "navigation": {
                "brand": "Model Master",
                "menuItems": [
                    {
                        "id": "dashboard",
                        "label": "Dashboard",
                        "icon": "fas fa-tachometer-alt",
                        "active": true
                    },
                    {
                        "id": "products",
                        "label": "Products",
                        "icon": "fas fa-box",
                        "active": false
                    },
                    {
                        "id": "categories",
                        "label": "Categories",
                        "icon": "fas fa-tags",
                        "active": false
                    },
                    {
                        "id": "analytics",
                        "label": "Analytics",
                        "icon": "fas fa-chart-bar",
                        "active": false
                    },
                    {
                        "id": "settings",
                        "label": "Settings",
                        "icon": "fas fa-cog",
                        "active": false
                    }
                ]
            },
            "dashboard": {
                "stats": [
                    {
                        "id": "total-products",
                        "title": "Total Products",
                        "value": "1,234",
                        "icon": "fas fa-box",
                        "color": "primary",
                        "trend": "+12%",
                        "trendDirection": "up"
                    },
                    {
                        "id": "active-models",
                        "title": "Active Models",
                        "value": "856",
                        "icon": "fas fa-cube",
                        "color": "success",
                        "trend": "+8%",
                        "trendDirection": "up"
                    },
                    {
                        "id": "categories",
                        "title": "Categories",
                        "value": "45",
                        "icon": "fas fa-tags",
                        "color": "info",
                        "trend": "+3%",
                        "trendDirection": "up"
                    },
                    {
                        "id": "revenue",
                        "title": "Revenue",
                        "value": "$125,430",
                        "icon": "fas fa-dollar-sign",
                        "color": "warning",
                        "trend": "+15%",
                        "trendDirection": "up"
                    }
                ]
            },
            "products": {
                "tableHeaders": [
                    {
                        "key": "id",
                        "label": "ID",
                        "sortable": true,
                        "width": "80px"
                    },
                    {
                        "key": "name",
                        "label": "Product Name",
                        "sortable": true,
                        "width": "200px"
                    },
                    {
                        "key": "model",
                        "label": "Model",
                        "sortable": true,
                        "width": "150px"
                    },
                    {
                        "key": "category",
                        "label": "Category",
                        "sortable": true,
                        "width": "120px"
                    },
                    {
                        "key": "price",
                        "label": "Price",
                        "sortable": true,
                        "width": "100px"
                    },
                    {
                        "key": "stock",
                        "label": "Stock",
                        "sortable": true,
                        "width": "80px"
                    },
                    {
                        "key": "status",
                        "label": "Status",
                        "sortable": true,
                        "width": "100px"
                    },
                    {
                        "key": "actions",
                        "label": "Actions",
                        "sortable": false,
                        "width": "120px"
                    }
                ],
                "sampleData": [
                    {
                        "id": "P001",
                        "name": "Premium Laptop",
                        "model": "XPS-15",
                        "category": "Electronics",
                        "price": "$1,299.99",
                        "stock": 45,
                        "status": "active"
                    },
                    {
                        "id": "P002",
                        "name": "Wireless Headphones",
                        "model": "WH-1000XM4",
                        "category": "Audio",
                        "price": "$349.99",
                        "stock": 23,
                        "status": "active"
                    },
                    {
                        "id": "P003",
                        "name": "Smart Watch",
                        "model": "Series-7",
                        "category": "Wearables",
                        "price": "$399.99",
                        "stock": 12,
                        "status": "low-stock"
                    },
                    {
                        "id": "P004",
                        "name": "Gaming Mouse",
                        "model": "G-Pro",
                        "category": "Gaming",
                        "price": "$79.99",
                        "stock": 67,
                        "status": "active"
                    },
                    {
                        "id": "P005",
                        "name": "4K Monitor",
                        "model": "U2720Q",
                        "category": "Displays",
                        "price": "$599.99",
                        "stock": 0,
                        "status": "out-of-stock"
                    }
                ]
            },
            "forms": {
                "productForm": {
                    "title": "Product Information",
                    "fields": [
                        {
                            "id": "productName",
                            "label": "Product Name",
                            "type": "text",
                            "required": true,
                            "placeholder": "Enter product name"
                        },
                        {
                            "id": "modelNumber",
                            "label": "Model Number",
                            "type": "text",
                            "required": true,
                            "placeholder": "Enter model number"
                        },
                        {
                            "id": "category",
                            "label": "Category",
                            "type": "select",
                            "required": true,
                            "options": [
                                {
                                    "value": "electronics",
                                    "label": "Electronics"
                                },
                                {
                                    "value": "audio",
                                    "label": "Audio"
                                },
                                {
                                    "value": "wearables",
                                    "label": "Wearables"
                                },
                                {
                                    "value": "gaming",
                                    "label": "Gaming"
                                },
                                {
                                    "value": "displays",
                                    "label": "Displays"
                                }
                            ]
                        },
                        {
                            "id": "price",
                            "label": "Price",
                            "type": "number",
                            "required": true,
                            "placeholder": "0.00",
                            "step": "0.01"
                        },
                        {
                            "id": "stock",
                            "label": "Stock Quantity",
                            "type": "number",
                            "required": true,
                            "placeholder": "0"
                        },
                        {
                            "id": "description",
                            "label": "Description",
                            "type": "textarea",
                            "required": false,
                            "placeholder": "Enter product description",
                            "rows": 4
                        }
                    ]
                }
            },
            "messages": {
                "success": {
                    "productAdded": "Product added successfully!",
                    "productUpdated": "Product updated successfully!",
                    "productDeleted": "Product deleted successfully!"
                },
                "error": {
                    "requiredField": "This field is required",
                    "invalidPrice": "Please enter a valid price",
                    "invalidStock": "Please enter a valid stock quantity",
                    "networkError": "Network error occurred. Please try again."
                },
                "confirm": {
                    "deleteProduct": "Are you sure you want to delete this product?"
                }
            }
        };
    </script>
    
    <!-- Modified JavaScript that uses embedded config -->
    <script src="Model.js"></script>
    
    <script>
        // Override the loadConfig method to use embedded configuration
        if (window.modelMaster) {
            window.modelMaster.config = MODEL_CONFIG;
            window.modelMaster.products = [...MODEL_CONFIG.products.sampleData];
            window.modelMaster.applyThemeFromConfig();
            window.modelMaster.renderNavigation();
            window.modelMaster.renderDashboard();
        }
        
        // Hide loading spinner once everything is loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                const spinner = document.getElementById('loadingSpinner');
                if (spinner) {
                    spinner.style.display = 'none';
                }
            }, 500);
        });
    </script>
</body>
</html>
