<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Master - Direct Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="Model.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mt-4 mb-4">Model Master - Direct Test</h1>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Testing Model Master</h5>
                    <p>This page tests the Model Master functionality directly without a controller.</p>
                    <p><strong>Check the browser console (F12) for any errors.</strong></p>
                </div>
            </div>
        </div>
        
        <div class="model-master-container">
            <!-- Navigation Container -->
            <div id="navigation"></div>
            
            <!-- Main Content Container -->
            <div id="content"></div>
            
            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="d-flex justify-content-center align-items-center position-fixed w-100 h-100" style="top: 0; left: 0; background: rgba(255,255,255,0.9); z-index: 9999;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Embedded Configuration for Testing -->
    <script>
        // Test configuration - embedded to avoid CORS issues
        window.MODEL_CONFIG = {
            "appConfig": {
                "title": "Model Master",
                "subtitle": "Product Management System",
                "version": "1.0.0",
                "theme": "modern"
            },
            "ui": {
                "colors": {
                    "primary": "#007bff",
                    "secondary": "#6c757d",
                    "success": "#28a745",
                    "danger": "#dc3545",
                    "warning": "#ffc107",
                    "info": "#17a2b8",
                    "light": "#f8f9fa",
                    "dark": "#343a40",
                    "white": "#ffffff",
                    "black": "#000000"
                },
                "spacing": {
                    "xs": "0.25rem",
                    "sm": "0.5rem",
                    "md": "1rem",
                    "lg": "1.5rem",
                    "xl": "2rem",
                    "xxl": "3rem"
                },
                "borderRadius": {
                    "sm": "0.25rem",
                    "md": "0.375rem",
                    "lg": "0.5rem",
                    "xl": "0.75rem"
                },
                "shadows": {
                    "sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
                    "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                    "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"
                },
                "fonts": {
                    "primary": "'Roboto', sans-serif",
                    "secondary": "'Open Sans', sans-serif"
                },
                "transitions": {
                    "fast": "0.15s",
                    "normal": "0.3s",
                    "slow": "0.5s"
                }
            },
            "navigation": {
                "brand": "Model Master",
                "menuItems": [
                    {
                        "id": "dashboard",
                        "label": "Dashboard",
                        "icon": "fas fa-tachometer-alt",
                        "active": true
                    },
                    {
                        "id": "products",
                        "label": "Products",
                        "icon": "fas fa-box",
                        "active": false
                    },
                    {
                        "id": "categories",
                        "label": "Categories",
                        "icon": "fas fa-tags",
                        "active": false
                    }
                ]
            },
            "dashboard": {
                "stats": [
                    {
                        "id": "total-products",
                        "title": "Total Products",
                        "value": "1,234",
                        "icon": "fas fa-box",
                        "color": "primary",
                        "trend": "+12%",
                        "trendDirection": "up"
                    },
                    {
                        "id": "active-models",
                        "title": "Active Models",
                        "value": "856",
                        "icon": "fas fa-cube",
                        "color": "success",
                        "trend": "+8%",
                        "trendDirection": "up"
                    }
                ]
            },
            "products": {
                "tableHeaders": [
                    {
                        "key": "id",
                        "label": "ID",
                        "sortable": true,
                        "width": "80px"
                    },
                    {
                        "key": "name",
                        "label": "Product Name",
                        "sortable": true,
                        "width": "200px"
                    },
                    {
                        "key": "actions",
                        "label": "Actions",
                        "sortable": false,
                        "width": "120px"
                    }
                ],
                "sampleData": [
                    {
                        "id": "P001",
                        "name": "Test Product",
                        "model": "TEST-1",
                        "category": "Electronics",
                        "price": "$99.99",
                        "stock": 10,
                        "status": "active"
                    }
                ]
            },
            "forms": {
                "productForm": {
                    "title": "Product Information",
                    "fields": [
                        {
                            "id": "productName",
                            "label": "Product Name",
                            "type": "text",
                            "required": true,
                            "placeholder": "Enter product name"
                        }
                    ]
                }
            },
            "messages": {
                "success": {
                    "productAdded": "Product added successfully!",
                    "productDeleted": "Product deleted successfully!"
                },
                "error": {
                    "requiredField": "This field is required",
                    "networkError": "Network error occurred."
                },
                "confirm": {
                    "deleteProduct": "Are you sure you want to delete this product?"
                }
            }
        };
    </script>
    
    <!-- Model Master JavaScript -->
    <script src="Model.js"></script>
    
    <script>
        // Hide loading spinner and show status
        window.addEventListener('load', () => {
            setTimeout(() => {
                const spinner = document.getElementById('loadingSpinner');
                if (spinner) {
                    spinner.style.display = 'none';
                }
                
                // Show initialization status
                console.log('Model Master Test Page Loaded');
                console.log('Check if navigation and content are visible');
                
                if (window.modelMaster) {
                    console.log('✅ Model Master initialized successfully');
                } else {
                    console.log('❌ Model Master failed to initialize');
                }
            }, 1000);
        });
    </script>
</body>
</html>
